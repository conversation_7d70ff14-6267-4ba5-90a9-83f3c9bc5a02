'use client';

import { format } from 'date-fns';
import {
  AlertCircle,
  CalendarIcon,
  ChevronDown,
  Cross,
  Crown,
  Droplets,
  Edit,
  FileText,
  Gift,
  Hand,
  Heart,
  Plus,
  Trash2,
  Wheat,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { cn, devLog } from '@/lib/utils';
import { formatDate, formatDateForDatabase } from '@/lib/utils/date-time';
import { getErrorMessage } from '@/lib/utils/error-handling';

// Interface for sacrament data using Prisma camelCase field names
interface Sacrament {
  id: number;
  memberId?: number;
  sacramentTypeId?: number;
  censusYearId?: number;
  createdAt?: string;
  updatedAt?: string;
  date: string | null;
  place: string | null;
  notes: string | null;
  // Additional fields added by API transformation
  sacrament_name?: string;
  sacrament_code?: string;
  sacrament_description?: string;
  census_year?: number;
}

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface EditableMemberSacramentsProps {
  memberId: number;
  memberName: string;
}

// Sacrament icon mapping - consistent with dashboard
const getSacramentIcon = (code: string | undefined | null) => {
  if (!code) {
    return <FileText className="h-4 w-4" />;
  }

  switch (code.toLowerCase()) {
    case 'baptism':
      return <Droplets className="h-4 w-4" />;
    case 'confirmation':
      return <Hand className="h-4 w-4" />;
    case 'communion':
      return <Wheat className="h-4 w-4" />;
    case 'matrimony':
      return <Heart className="h-4 w-4" />;
    case 'holy_orders':
      return <Crown className="h-4 w-4" />;
    case 'anointing':
      return <Gift className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

// Sacrament colour mapping
const getSacramentColor = (code: string | undefined | null) => {
  if (!code) {
    return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800';
  }

  switch (code.toLowerCase()) {
    case 'baptism':
      return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800';
    case 'confirmation':
      return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800';
    case 'communion':
      return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800';
    case 'matrimony':
      return 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300 dark:border-pink-800';
    case 'holy_orders':
      return 'bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800';
    case 'anointing':
      return 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800';
    default:
      return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800';
  }
};

export function EditableMemberSacraments({
  memberId,
  memberName,
}: EditableMemberSacramentsProps) {
  const t = useTranslations();
  const tCensus = useTranslations('census');
  const tCommon = useTranslations('common');
  const tAdmin = useTranslations('admin');
  const tSacraments = useTranslations('sacraments');
  const [sacraments, setSacraments] = useState<Sacrament[]>([]);
  const [sacramentTypes, setSacramentTypes] = useState<SacramentType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Dialog states (only for delete)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedSacrament, setSelectedSacrament] = useState<Sacrament | null>(
    null
  );

  // Add form states (inline)
  const [showAddForm, setShowAddForm] = useState(false);
  const [addFormData, setAddFormData] = useState({
    sacramentTypeId: '',
    date: null as Date | null,
    place: '',
  });
  const [isAddingSubmitting, setIsAddingSubmitting] = useState(false);

  // Edit form states (inline)
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingSacramentId, setEditingSacramentId] = useState<number | null>(
    null
  );
  const [editFormData, setEditFormData] = useState({
    sacramentTypeId: '',
    date: null as Date | null,
    place: '',
  });
  const [isEditingSubmitting, setIsEditingSubmitting] = useState(false);

  // Popover states for calendar
  const [addDatePopoverOpen, setAddDatePopoverOpen] = useState(false);
  const [editDatePopoverOpen, setEditDatePopoverOpen] = useState(false);

  // Temporary date states for "Done" button confirmation
  const [addTemporaryDate, setAddTemporaryDate] = useState<Date | null>(null);
  const [editTemporaryDate, setEditTemporaryDate] = useState<Date | null>(null);

  // Initialize temporary dates when popovers open
  useEffect(() => {
    if (addDatePopoverOpen) {
      setAddTemporaryDate(addFormData.date);
    }
  }, [addDatePopoverOpen, addFormData.date]);

  useEffect(() => {
    if (editDatePopoverOpen) {
      setEditTemporaryDate(editFormData.date);
    }
  }, [editDatePopoverOpen, editFormData.date]);



  // Fix for popover inside dialog - force pointer events when add popover is open
  useEffect(() => {
    if (addDatePopoverOpen) {
      // Force pointer-events to be enabled when popover opens
      const originalPointerEvents = document.body.style.pointerEvents;
      document.body.style.pointerEvents = 'auto';

      // Cleanup function to restore original pointer events
      return () => {
        document.body.style.pointerEvents = originalPointerEvents;
      };
    }
  }, [addDatePopoverOpen]);

  // Fix for popover inside dialog - force pointer events when edit popover is open
  useEffect(() => {
    if (editDatePopoverOpen) {
      // Force pointer-events to be enabled when popover opens
      const originalPointerEvents = document.body.style.pointerEvents;
      document.body.style.pointerEvents = 'auto';

      // Cleanup function to restore original pointer events
      return () => {
        document.body.style.pointerEvents = originalPointerEvents;
      };
    }
  }, [editDatePopoverOpen]);

  // Simplified data fetching to fix loading issue
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const [sacramentsResponse, typesResponse] = await Promise.all([
        fetch(`/api/admin/members/${memberId}/sacraments`),
        fetch('/api/admin/sacrament-types'),
      ]);

      if (!(sacramentsResponse.ok && typesResponse.ok)) {
        throw new Error('Failed to fetch data');
      }

      const sacramentsData = await sacramentsResponse.json();
      const typesData = await typesResponse.json();

      setSacraments(sacramentsData.sacraments || []);
      setSacramentTypes(typesData.sacramentTypes || []);
    } catch (err) {
      const errorMessage = getErrorMessage(
        err,
        'Failed to load sacrament data'
      );
      devLog.error('Error fetching sacrament data:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [memberId]);

  // Retry mechanism
  const handleRetry = useCallback(() => {
    setRetryCount((prev) => prev + 1);
    fetchData();
  }, [fetchData]);

  // Initial data fetch
  useEffect(() => {
    if (memberId) {
      fetchData();
    }
  }, [memberId, fetchData]);

  // Get available sacrament types (not already recorded)
  // Handle hybrid API response format safely
  const getAvailableSacramentTypes = () => {
    const recordedTypeIds = sacraments
      .map((s) => {
        // Safely get sacrament type ID from camelCase field
        return s.sacramentTypeId || 0;
      })
      .filter((id) => id > 0); // Remove any invalid IDs

    return sacramentTypes.filter((type) => !recordedTypeIds.includes(type.id));
  };

  // Reset add form
  const resetAddForm = () => {
    setAddFormData({
      sacramentTypeId: '',
      date: null,
      place: '',
    });
    setAddDatePopoverOpen(false);
  };

  // Reset edit form
  const resetEditForm = () => {
    setEditFormData({
      sacramentTypeId: '',
      date: null,
      place: '',
    });
    setEditDatePopoverOpen(false);
  };

  // Handle add sacrament (show inline form)
  const handleAddSacrament = (e?: React.MouseEvent) => {
    e?.preventDefault();
    // Close edit form if open
    setShowEditForm(false);
    setEditingSacramentId(null);
    resetEditForm();

    resetAddForm();
    setShowAddForm(true);
  };

  // Handle cancel add sacrament
  const handleCancelAdd = () => {
    setShowAddForm(false);
    resetAddForm();
  };

  // Handle edit sacrament (show inline form)
  const handleEditSacrament = (sacrament: Sacrament, e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    // Validate sacrament data with comprehensive checks
    if (!sacrament.id) {
      devLog.error('Sacrament ID is missing');
      return;
    }

    // Get sacrament type ID from API response
    const sacramentTypeId = sacrament.sacramentTypeId;
    if (!sacramentTypeId) {
      devLog.error('Sacrament type ID is missing from sacrament data');
      // Don't log sensitive data in production - use devLog for consistency
      devLog.error('Sacrament data structure:', sacrament);
      return;
    }

    // Close add form if open
    setShowAddForm(false);
    resetAddForm();

    setEditingSacramentId(sacrament.id);
    setEditFormData({
      sacramentTypeId: sacramentTypeId.toString(),
      date: sacrament.date ? new Date(sacrament.date) : null,
      place: sacrament.place || '',
    });
    setShowEditForm(true);
  };

  // Handle cancel edit sacrament
  const handleCancelEdit = () => {
    setShowEditForm(false);
    setEditingSacramentId(null);
    resetEditForm();
  };

  // Handle delete sacrament
  const handleDeleteSacrament = (
    sacrament: Sacrament,
    e?: React.MouseEvent
  ) => {
    e?.preventDefault();
    setSelectedSacrament(sacrament);
    setShowDeleteDialog(true);
  };

  // Submit add sacrament (inline form) with validation
  const submitAddSacrament = async () => {
    // Client-side validation
    if (!addFormData.sacramentTypeId) {
      setError('Please select a sacrament type');
      return;
    }

    const sacramentTypeId = Number.parseInt(addFormData.sacramentTypeId);
    if (isNaN(sacramentTypeId) || sacramentTypeId <= 0) {
      setError('Invalid sacrament type selected');
      return;
    }

    // Check for duplicates client-side
    const existingTypeIds = sacraments
      .map((s) => s.sacramentTypeId)
      .filter(Boolean);
    if (existingTypeIds.includes(sacramentTypeId)) {
      setError('This sacrament type has already been recorded');
      return;
    }

    try {
      setIsAddingSubmitting(true);

      const response = await fetch('/api/admin/sacraments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId,
          sacramentTypeId,
          date: addFormData.date
            ? formatDateForDatabase(addFormData.date)
            : null,
          place: addFormData.place
            ? addFormData.place.trim().substring(0, 255)
            : null, // Sanitize and limit length
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add sacrament');
      }

      const result = await response.json();

      // Validate API response before updating state
      if (result.sacrament && result.sacrament.id) {
        // Add the new sacrament to the list
        setSacraments((prev) => [...prev, result.sacrament]);
        setShowAddForm(false);
        resetAddForm();
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err, 'Failed to add sacrament');
      devLog.error('Error adding sacrament:', err);
      setError(errorMessage);
    } finally {
      setIsAddingSubmitting(false);
    }
  };

  // Submit edit sacrament (inline form)
  const submitEditSacrament = async () => {
    if (!editingSacramentId) return;

    try {
      setIsEditingSubmitting(true);

      const response = await fetch('/api/admin/sacraments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingSacramentId,
          date: editFormData.date
            ? formatDateForDatabase(editFormData.date)
            : null,
          place: editFormData.place
            ? editFormData.place.trim().substring(0, 255)
            : null, // Sanitize and limit length
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update sacrament');
      }

      const result = await response.json();

      // Update the sacrament in the list
      setSacraments((prev) =>
        prev.map((s) => (s.id === editingSacramentId ? result.sacrament : s))
      );
      setShowEditForm(false);
      setEditingSacramentId(null);
      resetEditForm();
    } catch (err) {
      const errorMessage = getErrorMessage(err, 'Failed to update sacrament');
      devLog.error('Error updating sacrament:', err);
      setError(errorMessage);
    } finally {
      setIsEditingSubmitting(false);
    }
  };

  // Submit delete sacrament
  const submitDeleteSacrament = async () => {
    if (!selectedSacrament) return;

    try {
      setIsEditingSubmitting(true);

      const response = await fetch(
        `/api/admin/sacraments?id=${selectedSacrament.id}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete sacrament');
      }

      // Remove the sacrament from the list
      setSacraments((prev) =>
        prev.filter((s) => s.id !== selectedSacrament.id)
      );
      setShowDeleteDialog(false);
      setSelectedSacrament(null);
    } catch (err) {
      const errorMessage = getErrorMessage(err, 'Failed to delete sacrament');
      devLog.error('Error deleting sacrament:', err);
      setError(errorMessage);
    } finally {
      setIsEditingSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Collapsible Header */}
      <div
        aria-expanded={isExpanded}
        aria-label={`${isExpanded ? 'Collapse' : 'Expand'} sacraments section`}
        className="flex cursor-pointer items-center gap-2 rounded-md border border-border p-2 transition-colors hover:bg-muted/10"
        onClick={() => setIsExpanded(!isExpanded)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsExpanded(!isExpanded);
          }
        }}
        role="button"
        tabIndex={0}
      >
        <Cross className="h-4 w-4 text-primary" />
        <h4 className="font-medium text-sm">{t('common.sacraments')}</h4>
        {loading ? (
          <Skeleton className="h-5 w-16" />
        ) : (
          <Badge className="text-xs" variant="outline">
            {sacraments.length} recorded
          </Badge>
        )}
        <ChevronDown
          className={`ml-auto h-4 w-4 text-muted-foreground transition-transform duration-200 ${
            isExpanded ? 'rotate-180' : ''
          }`}
        />
      </div>

      {/* Collapsible Content */}
      <div
        className={`overflow-hidden transition-all duration-200 ease-in-out ${
          isExpanded ? 'max-h-[600px] opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="max-h-[550px] space-y-3 overflow-y-auto pt-1">
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : error ? (
            <div className="space-y-3 py-4 text-sm">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <p>{t('emptyStates.unableToLoadSacramentInfo')}</p>
              </div>
              <p className="text-muted-foreground text-xs">{error}</p>
              <Button
                className="w-full"
                disabled={loading}
                onClick={handleRetry}
                size="sm"
                variant="outline"
              >
                {loading
                  ? 'Retrying...'
                  : `Retry${retryCount > 0 ? ` (${retryCount})` : ''}`}
              </Button>
            </div>
          ) : (
            <>
              {/* Add Sacrament Form or Button */}
              {!(showAddForm || showEditForm) &&
                getAvailableSacramentTypes().length > 0 && (
                  <Button
                    className="w-full"
                    onClick={(e) => handleAddSacrament(e)}
                    size="sm"
                    type="button"
                    variant="outline"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    {t('census.addSacrament')}
                  </Button>
                )}

              {/* Inline Add Sacrament Form */}
              {showAddForm && (
                <div className="rounded-md border border-muted-foreground/20 bg-muted/10 p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-muted-foreground text-sm">
                        {tCommon('addNewSacrament')}
                      </h5>
                      <Button
                        className="h-6 w-6 p-0"
                        onClick={handleCancelAdd}
                        size="sm"
                        type="button"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Select
                          onValueChange={(value) =>
                            setAddFormData((prev) => ({
                              ...prev,
                              sacramentTypeId: value,
                            }))
                          }
                          value={addFormData.sacramentTypeId}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue
                              placeholder={t('forms.selectSacramentType')}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {getAvailableSacramentTypes().map((type) => (
                              <SelectItem
                                key={type.id}
                                value={type.id.toString()}
                              >
                                {tSacraments(type.code as any)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label className="text-xs">
                            {tCommon('dateOptional')}
                          </Label>
                          <Popover
                            modal={false}
                            onOpenChange={setAddDatePopoverOpen}
                            open={addDatePopoverOpen}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                className={cn(
                                  'h-8 w-full justify-start text-left font-normal text-xs',
                                  !addFormData.date && 'text-muted-foreground'
                                )}
                                type="button"
                                variant="outline"
                              >
                                <CalendarIcon className="mr-2 h-3 w-3 flex-shrink-0" />
                                <span className="truncate">
                                  {addFormData.date ? (
                                    format(addFormData.date, 'dd/MM/yyyy')
                                  ) : (
                                    <span>{t('common.pickDate')}</span>
                                  )}
                                </span>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent
                              align="start"
                              className="w-auto p-0"
                              onWheel={(e) => {
                                // Allow wheel events to propagate to calendar for proper scrolling
                                e.stopPropagation();
                              }}
                            >
                              <Calendar
                                date={addTemporaryDate}
                                onDateChange={(date) => setAddTemporaryDate(date)}
                                onDone={() => {
                                  if (addTemporaryDate) {
                                    setAddFormData((prev) => ({ ...prev, date: addTemporaryDate }));
                                  }
                                  setAddDatePopoverOpen(false);
                                }} // Hide the footer in Calendar to match census portal behaviour
                                preventFutureDates={true} // Hide the Today button in Calendar
                                showFooter={false} // This prevents auto-scrolling and allows mouse wheel scrolling
                                showTodayButton={false} // Prevent future dates for sacrament dates
                                standalone={false}
                              />
                              {/* Custom footer with Today and Done buttons - matches census portal pattern */}
                              <div className="flex justify-between border-t p-3">
                                <Button
                                  className="cursor-pointer"
                                  onClick={() => {
                                    const today = new Date();
                                    setAddTemporaryDate(today);
                                  }}
                                  size="sm"
                                  variant="ghost"
                                >
                                  {tCommon('today')}
                                </Button>
                                <Button
                                  className="cursor-pointer"
                                  onClick={() => {
                                    if (addTemporaryDate) {
                                      setAddFormData((prev) => ({ ...prev, date: addTemporaryDate }));
                                    }
                                    setAddDatePopoverOpen(false);
                                  }}
                                  size="sm"
                                >
                                  {t('common.done')}
                                </Button>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-xs">
                            {tCommon('placeOptional')}
                          </Label>
                          <Input
                            className="h-8 text-xs"
                            onChange={(e) =>
                              setAddFormData((prev) => ({
                                ...prev,
                                place: e.target.value,
                              }))
                            }
                            placeholder={t('forms.enterPlace')}
                            value={addFormData.place}
                          />
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          className="flex-1"
                          disabled={isAddingSubmitting}
                          onClick={handleCancelAdd}
                          size="sm"
                          type="button"
                          variant="outline"
                        >
                          {tCommon('cancel')}
                        </Button>
                        <Button
                          className="flex-1"
                          disabled={
                            isAddingSubmitting || !addFormData.sacramentTypeId
                          }
                          onClick={submitAddSacrament}
                          size="sm"
                          type="button"
                        >
                          {isAddingSubmitting
                            ? tCensus('adding')
                            : tCommon('add')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Sacraments List */}
              {sacraments.length === 0 && !showAddForm && !showEditForm ? (
                <div className="py-6 text-center">
                  <FileText className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
                  <p className="text-muted-foreground text-sm">
                    {t('emptyStates.noSacramentsRecorded')}
                  </p>
                </div>
              ) : sacraments.length > 0 ? (
                <div className="grid gap-3">
                  {sacraments.map((sacrament) => {
                    // Show edit form for the sacrament being edited
                    if (showEditForm && editingSacramentId === sacrament.id) {
                      return (
                        <div
                          className="rounded-md border border-muted-foreground/20 bg-muted/10 p-4"
                          key={sacrament.id}
                        >
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <h5 className="font-medium text-muted-foreground text-sm">
                                {tCommon('editSacrament', {
                                  sacramentName:
                                    sacrament.sacrament_name || 'Sacrament',
                                })}
                              </h5>
                              <Button
                                className="h-6 w-6 p-0"
                                onClick={handleCancelEdit}
                                size="sm"
                                type="button"
                                variant="ghost"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>

                            <div className="space-y-3">
                              <div className="grid grid-cols-2 gap-3">
                                <div className="space-y-2">
                                  <Label className="text-xs">
                                    {tCommon('dateOptional')}
                                  </Label>
                                  <Popover
                                    modal={false}
                                    onOpenChange={setEditDatePopoverOpen}
                                    open={editDatePopoverOpen}
                                  >
                                    <PopoverTrigger asChild>
                                      <Button
                                        className={cn(
                                          'h-8 w-full justify-start text-left font-normal text-xs',
                                          !editFormData.date &&
                                            'text-muted-foreground'
                                        )}
                                        type="button"
                                        variant="outline"
                                      >
                                        <CalendarIcon className="mr-2 h-3 w-3 flex-shrink-0" />
                                        <span className="truncate">
                                          {editFormData.date ? (
                                            format(
                                              editFormData.date,
                                              'dd/MM/yyyy'
                                            )
                                          ) : (
                                            <span>{t('common.pickDate')}</span>
                                          )}
                                        </span>
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent
                                      align="start"
                                      className="w-auto p-0"
                                      onWheel={(e) => {
                                        // Allow wheel events to propagate to calendar for proper scrolling
                                        e.stopPropagation();
                                      }}
                                    >
                                      <Calendar
                                        date={editTemporaryDate}
                                        onDateChange={(date) => setEditTemporaryDate(date)}
                                        onDone={() => {
                                          if (editTemporaryDate) {
                                            setEditFormData((prev) => ({ ...prev, date: editTemporaryDate }));
                                          }
                                          setEditDatePopoverOpen(false);
                                        }} // Hide the footer in Calendar to match census portal behaviour
                                        preventFutureDates={true} // Hide the Today button in Calendar
                                        showFooter={false} // This prevents auto-scrolling and allows mouse wheel scrolling
                                        showTodayButton={false} // Prevent future dates for sacrament dates
                                        standalone={false}
                                      />
                                      {/* Custom footer with Today and Done buttons - matches census portal pattern */}
                                      <div className="flex justify-between border-t p-3">
                                        <Button
                                          className="cursor-pointer"
                                          onClick={() => {
                                            const today = new Date();
                                            setEditTemporaryDate(today);
                                          }}
                                          size="sm"
                                          variant="ghost"
                                        >
                                          {tCommon('today')}
                                        </Button>
                                        <Button
                                          className="cursor-pointer"
                                          onClick={() => {
                                            if (editTemporaryDate) {
                                              setEditFormData((prev) => ({ ...prev, date: editTemporaryDate }));
                                            }
                                            setEditDatePopoverOpen(false);
                                          }}
                                          size="sm"
                                        >
                                          {t('common.done')}
                                        </Button>
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </div>

                                <div className="space-y-2">
                                  <Label className="text-xs">
                                    {tCommon('placeOptional')}
                                  </Label>
                                  <Input
                                    className="h-8 text-xs"
                                    onChange={(e) =>
                                      setEditFormData((prev) => ({
                                        ...prev,
                                        place: e.target.value,
                                      }))
                                    }
                                    placeholder={t('forms.enterPlace')}
                                    value={editFormData.place}
                                  />
                                </div>
                              </div>

                              <div className="flex gap-2 pt-2">
                                <Button
                                  className="flex-1"
                                  disabled={isEditingSubmitting}
                                  onClick={handleCancelEdit}
                                  size="sm"
                                  type="button"
                                  variant="outline"
                                >
                                  {tCommon('cancel')}
                                </Button>
                                <Button
                                  className="flex-1"
                                  disabled={isEditingSubmitting}
                                  onClick={submitEditSacrament}
                                  size="sm"
                                  type="button"
                                >
                                  {isEditingSubmitting
                                    ? tAdmin('updating')
                                    : tAdmin('updateMember')}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    }

                    // Show normal sacrament display
                    const translatedSacramentName = sacrament.sacrament_code
                      ? tSacraments(sacrament.sacrament_code as any)
                      : sacrament.sacrament_name;
                    const titleParts = [translatedSacramentName];

                    if (sacrament.date) {
                      titleParts.push(formatDate(new Date(sacrament.date)));
                    }

                    if (sacrament.place) {
                      titleParts.push(sacrament.place);
                    }

                    return (
                      <div
                        className="rounded-md bg-muted/10 p-3"
                        key={sacrament.id}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-sm">
                            <div
                              className={`rounded-full p-1.5 ${getSacramentColor(sacrament.sacrament_code)}`}
                            >
                              {getSacramentIcon(sacrament.sacrament_code)}
                            </div>
                            <span className="font-medium">
                              {titleParts.join(' • ')}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              aria-label={`Edit ${translatedSacramentName} sacrament`}
                              className="h-8 w-8 p-0"
                              disabled={showEditForm || showAddForm}
                              onClick={(e) => handleEditSacrament(sacrament, e)}
                              size="sm"
                              title={`Edit ${translatedSacramentName} sacrament`}
                              type="button"
                              variant="ghost"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              aria-label={`Delete ${translatedSacramentName} sacrament`}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                              disabled={showEditForm || showAddForm}
                              onClick={(e) =>
                                handleDeleteSacrament(sacrament, e)
                              }
                              size="sm"
                              title={`Delete ${translatedSacramentName} sacrament`}
                              type="button"
                              variant="ghost"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : null}
            </>
          )}
        </div>
      </div>

      {/* Delete Sacrament Dialog */}
      <Dialog onOpenChange={setShowDeleteDialog} open={showDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('common.deleteSacrament')}</DialogTitle>
            <DialogDescription>
              {tCommon('deleteSacramentConfirmation', {
                sacramentName: selectedSacrament?.sacrament_name || 'Sacrament',
                memberName,
              })}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              disabled={isEditingSubmitting}
              onClick={() => setShowDeleteDialog(false)}
              type="button"
              variant="outline"
            >
              {tCommon('cancel')}
            </Button>
            <Button
              disabled={isEditingSubmitting}
              onClick={submitDeleteSacrament}
              type="button"
              variant="destructive"
            >
              {isEditingSubmitting
                ? tCommon('deleting')
                : tCommon('deleteSacrament')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
