# Census Participant Flow Diagram

This document contains a Mermaid diagram that illustrates the complete census participant experience, including:

- Login process with unique code authentication
- Welcome modal and onboarding experience
- Main census form workflow
- Account management and deletion functionality

## Flow Diagram

```mermaid
flowchart TD
    %% Entry Points
    A[Homepage] --> B{Census Open?}
    B -->|No| C[Census Closed Message]
    B -->|Yes| D[Unique Code Entry Form]
    
    %% Authentication Flow
    D --> E{Valid Code?}
    E -->|No| F[Error Message + Rate Limiting]
    F --> D
    E -->|Yes| G{Rate Limited?}
    G -->|Yes| H[Too Many Attempts Error]
    H --> I[Wait Period]
    I --> D
    G -->|No| J[Authentication Success]
    
    %% Post-Login Flow
    J --> K{First Time Login?}
    K -->|Yes| L[Welcome Modal]
    K -->|No| M{Household Registered?}
    
    %% Welcome Modal Experience
    L --> N[Welcome Modal Content]
    N --> O[Next Steps Section]
    O --> P[Interactive Tour Options]
    P --> Q[Get Started Button]
    Q --> M
    
    %% Household Registration
    M -->|No| R[Household Registration Form]
    R --> S[Enter Household Details]
    S --> T[Enter Head of Household Info]
    T --> U[Submit Registration]
    U --> V[Registration Success]
    V --> W[Census Form Access]
    
    %% Main Census Form Experience
    M -->|Yes| W
    W --> X[Census Form Dashboard]
    X --> Y[Household Information Card]
    X --> Z[Members Management]
    X --> AA[Community Feedback]
    
    %% Member Management
    Z --> BB[Add New Member]
    Z --> CC[Edit Existing Member]
    Z --> DD[Delete Member]
    BB --> EE[Member Form with Sacraments]
    CC --> EE
    EE --> FF[Personal Details]
    FF --> GG[Sacrament Records]
    GG --> HH[Save Member]
    HH --> X
    
    %% Navigation Options
    X --> II[Header Navigation]
    II --> JJ[Home Link]
    II --> KK[Census Form Link]
    II --> LL[Account Management]
    II --> MM[Sign Out]
    
    %% Account Management Flow
    LL --> NN[Account Page]
    NN --> OO[Account Information Display]
    NN --> PP[Account Management Section]
    PP --> QQ[Delete Account Button]
    
    %% Account Deletion Process
    QQ --> RR[Delete Account Dialog]
    RR --> SS[Warning Message]
    SS --> TT[Confirmation Required]
    TT --> UU{Type 'DELETE NOW'?}
    UU -->|No| VV[Validation Error]
    VV --> TT
    UU -->|Yes| WW[Delete Account API Call]
    WW --> XX[Account Deletion Success]
    XX --> YY[Clear Local Storage]
    YY --> ZZ[Sign Out]
    ZZ --> AAA[Redirect to Homepage]
    AAA --> BBB[Success Toast Message]
    
    %% Sign Out Flow
    MM --> CCC[Sign Out Process]
    CCC --> DDD[Clear Session]
    DDD --> EEE[Redirect to Homepage]
    
    %% Error Handling
    WW -->|Error| FFF[Deletion Failed]
    FFF --> GGG[Error Toast]
    GGG --> RR
    
    %% Tour System
    P --> HHH[Context-Aware Tours]
    HHH --> III[Hobby Fields Tour]
    HHH --> JJJ[Occupation Tour]
    HHH --> KKK[Sacraments Tour]
    HHH --> LLL[Add Member Tour]
    HHH --> MMM[Community Feedback Tour]
    
    %% Progress Tracking
    X --> NNN[Progress Circle]
    NNN --> OOO[Completion Tracking]
    OOO --> PPP[Real-time Updates]
    
    %% Styling
    classDef entryPoint fill:#e1f5fe
    classDef authFlow fill:#f3e5f5
    classDef mainFlow fill:#e8f5e8
    classDef deleteFlow fill:#ffebee
    classDef tourFlow fill:#fff3e0
    
    class A,D entryPoint
    class E,G,J,K authFlow
    class W,X,Y,Z,AA mainFlow
    class QQ,RR,SS,TT,UU,WW,XX deleteFlow
    class L,N,O,P,HHH,III,JJJ,KKK,LLL,MMM tourFlow
```

## Key Features Highlighted

### 1. Authentication & Security
- **Unique Code Validation**: Participants enter their unique census code
- **Rate Limiting**: Protection against brute force attacks with lockout periods
- **Session Management**: Secure session handling with automatic logout for deleted accounts

### 2. Welcome Experience
- **Welcome Modal**: First-time users see an interactive welcome modal
- **Next Steps Section**: Progressive action items with completion tracking
- **Interactive Tours**: Context-aware guided tours for different form sections

### 3. Main Census Form
- **Household Registration**: Initial setup of household information
- **Member Management**: Add, edit, and delete household members
- **Sacrament Records**: Detailed sacrament information for each member
- **Progress Tracking**: Real-time completion status with visual indicators

### 4. Account Management
- **Account Information**: Display of user details and household status
- **Account Deletion**: Comprehensive deletion process with multiple safeguards
- **Data Cleanup**: Complete removal of all associated data including:
  - Household information
  - Member details
  - Sacrament records
  - Unique code assignment

### 5. Navigation & UX
- **Responsive Design**: Mobile and desktop optimized interfaces
- **Header Navigation**: Easy access to all major sections
- **Progress Indicators**: Visual feedback on form completion status
- **Toast Notifications**: User feedback for all major actions

## Technical Implementation Notes

- **NextAuth.js**: Custom authentication provider for census codes
- **Rate Limiting**: Server-side protection with session tokens
- **Database Transactions**: Atomic operations for data integrity
- **Audit Logging**: Complete audit trail for all account actions
- **Responsive Components**: Mobile-first design with drawer/dialog patterns

---

# 人口普查参与者流程图 (Chinese Version)

本文档包含一个美观现代的流程图，展示了完整的人口普查参与者体验，包括：

- 使用唯一代码进行身份验证的登录流程
- 欢迎模态框和引导体验
- 主要普查表单工作流程
- 账户管理和删除功能

## 流程图 (现代风格)

```mermaid
flowchart TD
    %% 入口点
    A["🏠 主页<br/>Homepage"] --> B{"📊 普查开放？<br/>Census Open?"}
    B -->|❌ 否| C["🚫 普查关闭提示<br/>Census Closed"]
    B -->|✅ 是| D["🔑 唯一代码输入<br/>Unique Code Entry"]

    %% 身份验证流程
    D --> E{"✅ 代码有效？<br/>Valid Code?"}
    E -->|❌ 否| F["⚠️ 错误信息 + 限制<br/>Error + Rate Limit"]
    F --> D
    E -->|✅ 是| G{"🔒 被限制？<br/>Rate Limited?"}
    G -->|✅ 是| H["🚨 尝试次数过多<br/>Too Many Attempts"]
    H --> I["⏰ 等待期<br/>Wait Period"]
    I --> D
    G -->|❌ 否| J["🎉 认证成功<br/>Auth Success"]

    %% 登录后流程
    J --> K{"🆕 首次登录？<br/>First Time?"}
    K -->|✅ 是| L["👋 欢迎模态框<br/>Welcome Modal"]
    K -->|❌ 否| M{"🏡 家庭已注册？<br/>Household Registered?"}

    %% 欢迎模态框体验
    L --> N["📝 欢迎内容<br/>Welcome Content"]
    N --> O["📋 下一步指南<br/>Next Steps Guide"]
    O --> P["🎯 互动导览选项<br/>Interactive Tours"]
    P --> Q["🚀 开始按钮<br/>Get Started"]
    Q --> M

    %% 家庭注册
    M -->|❌ 否| R["📋 家庭注册表单<br/>Household Registration"]
    R --> S["🏠 输入家庭详情<br/>Enter Household Details"]
    S --> T["👤 户主信息<br/>Head of Household"]
    T --> U["💾 提交注册<br/>Submit Registration"]
    U --> V["✅ 注册成功<br/>Registration Success"]
    V --> W["📊 普查表单访问<br/>Census Form Access"]

    %% 主要普查表单体验
    M -->|✅ 是| W
    W --> X["📊 普查表单仪表板<br/>Census Dashboard"]
    X --> Y["🏠 家庭信息卡<br/>Household Info Card"]
    X --> Z["👥 成员管理<br/>Members Management"]
    X --> AA["💬 社区反馈<br/>Community Feedback"]

    %% 成员管理
    Z --> BB["➕ 添加新成员<br/>Add New Member"]
    Z --> CC["✏️ 编辑成员<br/>Edit Member"]
    Z --> DD["🗑️ 删除成员<br/>Delete Member"]
    BB --> EE["📝 成员表单<br/>Member Form"]
    CC --> EE
    EE --> FF["👤 个人详情<br/>Personal Details"]
    FF --> GG["⛪ 圣事记录<br/>Sacrament Records"]
    GG --> HH["💾 保存成员<br/>Save Member"]
    HH --> X

    %% 导航选项
    X --> II["🧭 头部导航<br/>Header Navigation"]
    II --> JJ["🏠 主页链接<br/>Home Link"]
    II --> KK["📊 普查表单<br/>Census Form"]
    II --> LL["⚙️ 账户管理<br/>Account Management"]
    II --> MM["🚪 退出登录<br/>Sign Out"]

    %% 账户管理流程
    LL --> NN["👤 账户页面<br/>Account Page"]
    NN --> OO["📋 账户信息显示<br/>Account Info Display"]
    NN --> PP["⚙️ 账户管理区域<br/>Account Management"]
    PP --> QQ["🗑️ 删除账户按钮<br/>Delete Account Button"]

    %% 账户删除流程
    QQ --> RR["⚠️ 删除账户对话框<br/>Delete Account Dialog"]
    RR --> SS["🚨 警告信息<br/>Warning Message"]
    SS --> TT["✋ 需要确认<br/>Confirmation Required"]
    TT --> UU{"✍️ 输入 'DELETE NOW'？<br/>Type 'DELETE NOW'?"}
    UU -->|❌ 否| VV["❌ 验证错误<br/>Validation Error"]
    VV --> TT
    UU -->|✅ 是| WW["🔄 删除账户API调用<br/>Delete Account API"]
    WW --> XX["✅ 账户删除成功<br/>Deletion Success"]
    XX --> YY["🧹 清除本地存储<br/>Clear Local Storage"]
    YY --> ZZ["🚪 退出登录<br/>Sign Out"]
    ZZ --> AAA["↩️ 重定向到主页<br/>Redirect to Homepage"]
    AAA --> BBB["🎉 成功提示消息<br/>Success Toast"]

    %% 退出登录流程
    MM --> CCC["🚪 退出登录流程<br/>Sign Out Process"]
    CCC --> DDD["🧹 清除会话<br/>Clear Session"]
    DDD --> EEE["↩️ 重定向到主页<br/>Redirect to Homepage"]

    %% 错误处理
    WW -->|❌ 错误| FFF["💥 删除失败<br/>Deletion Failed"]
    FFF --> GGG["⚠️ 错误提示<br/>Error Toast"]
    GGG --> RR

    %% 导览系统
    P --> HHH["🎯 上下文感知导览<br/>Context-Aware Tours"]
    HHH --> III["🎨 爱好字段导览<br/>Hobby Fields Tour"]
    HHH --> JJJ["💼 职业导览<br/>Occupation Tour"]
    HHH --> KKK["⛪ 圣事导览<br/>Sacraments Tour"]
    HHH --> LLL["➕ 添加成员导览<br/>Add Member Tour"]
    HHH --> MMM["💬 社区反馈导览<br/>Community Feedback Tour"]

    %% 进度跟踪
    X --> NNN["🔄 进度圆圈<br/>Progress Circle"]
    NNN --> OOO["📊 完成度跟踪<br/>Completion Tracking"]
    OOO --> PPP["⚡ 实时更新<br/>Real-time Updates"]

    %% 现代Excalidraw风格样式
    classDef entryPoint fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#0d47a1,stroke-dasharray: 5 5
    classDef authFlow fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c,rx:15,ry:15
    classDef mainFlow fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20,rx:10,ry:10
    classDef deleteFlow fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#b71c1c,stroke-dasharray: 3 3
    classDef tourFlow fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#e65100,rx:20,ry:20
    classDef successFlow fill:#e0f2f1,stroke:#00695c,stroke-width:4px,color:#004d40,rx:25,ry:25

    class A,D entryPoint
    class E,G,J,K,F,H,I authFlow
    class W,X,Y,Z,AA,EE,FF,GG,HH mainFlow
    class QQ,RR,SS,TT,UU,WW,XX,FFF,GGG deleteFlow
    class L,N,O,P,HHH,III,JJJ,KKK,LLL,MMM tourFlow
    class V,BBB,CCC,DDD,EEE successFlow
```

## 主要功能亮点 (Key Features)

### 1. 身份验证与安全 🔐
- **唯一代码验证**: 参与者输入他们的唯一普查代码
- **速率限制**: 防止暴力攻击的保护机制，包含锁定期
- **会话管理**: 安全的会话处理，对已删除账户自动退出登录

### 2. 欢迎体验 👋
- **欢迎模态框**: 首次用户看到交互式欢迎模态框
- **下一步指南**: 带有完成度跟踪的渐进式操作项目
- **互动导览**: 针对不同表单部分的上下文感知引导导览

### 3. 主要普查表单 📊
- **家庭注册**: 家庭信息的初始设置
- **成员管理**: 添加、编辑和删除家庭成员
- **圣事记录**: 每个成员的详细圣事信息
- **进度跟踪**: 带有视觉指示器的实时完成状态

### 4. 账户管理 ⚙️
- **账户信息**: 显示用户详情和家庭状态
- **账户删除**: 具有多重保护措施的全面删除流程
- **数据清理**: 完全删除所有相关数据，包括：
  - 家庭信息
  - 成员详情
  - 圣事记录
  - 唯一代码分配

### 5. 导航与用户体验 🧭
- **响应式设计**: 针对移动端和桌面端优化的界面
- **头部导航**: 轻松访问所有主要部分
- **进度指示器**: 表单完成状态的视觉反馈
- **提示通知**: 所有主要操作的用户反馈

## 技术实现说明 🛠️

- **NextAuth.js**: 普查代码的自定义身份验证提供程序
- **速率限制**: 使用会话令牌的服务器端保护
- **数据库事务**: 确保数据完整性的原子操作
- **审计日志**: 所有账户操作的完整审计跟踪
- **响应式组件**: 移动优先设计，采用抽屉/对话框模式

## 设计特色 🎨

此中文版本采用现代Excalidraw风格设计，具有以下特点：

- **🎨 视觉层次**: 使用表情符号和双语标签增强可读性
- **🌈 色彩编码**: 不同类型的流程使用不同颜色和样式
- **📱 现代风格**: 圆角边框、虚线边框和渐变效果
- **🔄 交互感**: 动态样式表现不同的用户操作状态
- **🌐 国际化**: 完整的中英文对照，便于理解
